# Burp Suite 集成使用指南

## 概述

AntiDebug_Breaker 现在支持将拦截的请求转发到 Burp Suite 进行深度分析。这个功能可以帮助您：

- 在 Burp Suite 中查看和修改所有 HTTP 请求
- 分析加密前的原始数据
- 使用 Burp 的强大功能进行渗透测试

## 新增脚本

### 1. Hook Requests (增强版)
- **功能**: 拦截 XHR 和 Fetch 请求，捕获加密前数据
- **特性**: 
  - 智能数据解析
  - 自动保存到 `window.capturedRequests`
  - 支持导出为 JSON 文件

### 2. Burp Suite Forwarder
- **功能**: 将所有 HTTP 请求转发到 Burp Suite 代理
- **特性**:
  - 自动代理所有请求
  - 保持原始请求头
  - 支持手动发送请求

## 使用步骤

### 第一步：配置 Burp Suite

1. 打开 Burp Suite
2. 进入 `Proxy` → `Options`
3. 确保代理监听在 `127.0.0.1:8080` (默认)
4. 启用 `Intercept is on` (如果需要拦截)

### 第二步：启用扩展脚本

1. 重新加载 AntiDebug_Breaker 扩展
2. 在目标网站启用以下脚本：
   - ✅ Hook Requests
   - ✅ Burp Suite Forwarder
   - ✅ Hook Crypto Libraries (可选)

### 第三步：启用 Burp 转发

在浏览器控制台执行：

```javascript
// 启用 Burp 转发 (默认 127.0.0.1:8080)
enableBurpForward()

// 或指定自定义地址
enableBurpForward("127.0.0.1", "8080")
```

### 第四步：开始分析

1. 刷新目标页面
2. 所有请求将自动转发到 Burp Suite
3. 在 Burp 的 `Proxy` → `HTTP history` 中查看请求

## 控制台命令

### 基本命令

```javascript
// 启用 Burp 转发
enableBurpForward()

// 禁用 Burp 转发
disableBurpForward()

// 查看捕获的请求
console.log(window.capturedRequests)

// 导出捕获的数据
exportCapturedData()

// 导出 Burp 格式数据
exportForBurp()

// 清空捕获的数据
clearCapturedData()
```

### 高级命令

```javascript
// 手动发送请求到 Burp
sendToBurp("https://example.com/api", "POST", 
  {"Content-Type": "application/json"}, 
  JSON.stringify({data: "test"}))

// 查看 Burp 配置
console.log(window.burpForwarder)
```

## 数据分析

### 1. 查看原始数据

```javascript
// 查看最新的请求
console.log(window.capturedRequests[window.capturedRequests.length - 1])

// 查看所有 JSON.stringify 调用
window.capturedRequests.filter(req => req.type === 'JSON.stringify')
```

### 2. 导出分析

```javascript
// 导出所有数据
exportCapturedData()

// 只导出请求数据 (适合 Burp 导入)
exportForBurp()
```

## 故障排除

### 问题 1: 请求没有出现在 Burp 中

**解决方案:**
1. 检查 Burp 代理是否正确启动
2. 确认代理地址是否正确
3. 检查浏览器控制台是否有错误

```javascript
// 检查 Burp 转发状态
console.log(window.burpForwarder)
```

### 问题 2: 只看到加密数据

**解决方案:**
1. 启用 `Hook Crypto Libraries` 脚本
2. 查看 `JSON.stringify` 的调用记录
3. 使用 `window.capturedRequests` 查看原始数据

### 问题 3: 某些请求没有被拦截

**解决方案:**
1. 确保所有相关脚本都已启用
2. 刷新页面让脚本重新加载
3. 检查是否有其他脚本冲突

## 最佳实践

1. **先启用数据捕获脚本**，再启用 Burp 转发
2. **定期导出数据**，避免数据丢失
3. **使用 Burp 的 Repeater** 功能重放和修改请求
4. **结合 Burp 的 Intruder** 进行自动化测试

## 安全提醒

- 仅在授权的测试环境中使用
- 不要在生产环境中启用请求转发
- 注意保护敏感数据，避免泄露
