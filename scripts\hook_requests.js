// ==UserScript==
// @name         hook_requests
// @namespace    https://github.com/0xsdeo/Hook_JS
// @version      2024-12-20
// @description  拦截业务层请求，在加密前捕获原始数据，支持转发到Burp Suite
// <AUTHOR>
// @match        http://*/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    console.log('[Hook Requests] Script loaded - Enhanced version for raw data capture');

    const originalXHR = window.XMLHttpRequest;
    const originalFetch = window.fetch;

    // 创建一个全局数组来存储捕获的数据
    window.capturedRequests = window.capturedRequests || [];

    // Burp Suite配置
    window.burpConfig = {
        enabled: false,
        proxyUrl: 'http://127.0.0.1:8080',  // Burp Suite默认代理地址
        forwardOriginal: true  // 是否同时发送原始请求
    };

    // 启用/禁用Burp转发
    window.enableBurpForward = function(proxyUrl = 'http://127.0.0.1:8080') {
        window.burpConfig.enabled = true;
        window.burpConfig.proxyUrl = proxyUrl;
        console.log('🔄 Burp转发已启用，代理地址:', proxyUrl);
    };

    window.disableBurpForward = function() {
        window.burpConfig.enabled = false;
        console.log('❌ Burp转发已禁用');
    };

    // 转发请求到Burp Suite
    async function forwardToBurp(requestData) {
        if (!window.burpConfig.enabled) return;

        try {
            const burpRequest = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Forwarded-From': 'AntiDebug-Breaker',
                    'X-Original-URL': requestData.url,
                    'X-Original-Method': requestData.method
                },
                body: JSON.stringify({
                    originalRequest: requestData,
                    timestamp: new Date().toISOString(),
                    source: 'browser-hook'
                })
            };

            // 发送到Burp代理
            await fetch(window.burpConfig.proxyUrl + '/burp-forward', burpRequest);
            console.log('📤 请求已转发到Burp Suite');
        } catch (error) {
            console.warn('⚠️ 转发到Burp失败:', error.message);
        }
    }

    // 添加一个导出函数
    window.exportCapturedData = function() {
        const data = JSON.stringify(window.capturedRequests, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `captured_requests_${new Date().getTime()}.json`;
        a.click();
        URL.revokeObjectURL(url);
        console.log('📁 捕获的数据已导出到文件');
    };

    // 导出为Burp Suite可导入的格式
    window.exportForBurp = function() {
        const burpData = window.capturedRequests.map(req => ({
            url: req.url,
            method: req.method,
            headers: req.headers,
            body: req.body,
            timestamp: req.timestamp
        }));

        const data = JSON.stringify(burpData, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `burp_requests_${new Date().getTime()}.json`;
        a.click();
        URL.revokeObjectURL(url);
        console.log('📁 Burp格式数据已导出');
    };

    // 添加清空函数
    window.clearCapturedData = function() {
        window.capturedRequests = [];
        console.log('🗑️ 已清空捕获的数据');
    };

    // 尝试解析和显示数据的函数
    function parseRequestData(data) {
        if (!data) return 'No data';

        // 如果是字符串，尝试解析JSON
        if (typeof data === 'string') {
            try {
                const parsed = JSON.parse(data);
                return { type: 'JSON', data: parsed };
            } catch (e) {
                return { type: 'String', data: data };
            }
        }

        // 如果是FormData
        if (data instanceof FormData) {
            const formObj = {};
            for (let [key, value] of data.entries()) {
                formObj[key] = value;
            }
            return { type: 'FormData', data: formObj };
        }

        // 如果是URLSearchParams
        if (data instanceof URLSearchParams) {
            const params = {};
            for (let [key, value] of data.entries()) {
                params[key] = value;
            }
            return { type: 'URLSearchParams', data: params };
        }

        // 如果是ArrayBuffer或Uint8Array
        if (data instanceof ArrayBuffer || data instanceof Uint8Array) {
            const bytes = new Uint8Array(data);
            // 尝试转换为字符串看是否是文本
            try {
                const text = new TextDecoder('utf-8').decode(bytes);
                // 检查是否包含可打印字符
                if (/^[\x20-\x7E\s]*$/.test(text)) {
                    return { type: 'Binary->Text', data: text, raw: `Uint8Array(${bytes.length})` };
                }
            } catch (e) {}
            return { type: 'Binary', data: `Uint8Array(${bytes.length}) [${Array.from(bytes.slice(0, 20)).join(', ')}${bytes.length > 20 ? '...' : ''}]` };
        }

        // 其他类型
        return { type: typeof data, data: data };
    }

    // 创建一个增强的日志函数
    function logRequest(type, data) {
        // 保存到全局数组
        const requestRecord = {
            type: type,
            url: data.url,
            method: data.method,
            headers: data.headers,
            body: data.body,
            timestamp: data.timestamp,
            parsedBody: parseRequestData(data.body)
        };
        window.capturedRequests.push(requestRecord);

        console.group(`🔍 [${type}] Request Intercepted - ${new Date(data.timestamp).toLocaleString()}`);
        console.log('🌐 URL:', data.url);
        console.log('📝 Method:', data.method);
        console.log('📋 Headers:', data.headers);

        const parsedBody = parseRequestData(data.body);
        console.log('📦 Body Type:', parsedBody.type);
        console.log('📦 Body Data:', parsedBody.data);
        if (parsedBody.raw) {
            console.log('📦 Raw Info:', parsedBody.raw);
        }

        // 如果是加密数据，提供提示
        if (parsedBody.type === 'Binary' && data.body instanceof Uint8Array) {
            console.warn('⚠️  This appears to be encrypted/binary data. Original data may have been encrypted before this hook.');
        }

        console.log('💾 已保存到 window.capturedRequests[' + (window.capturedRequests.length - 1) + ']');

        // 转发到Burp Suite
        forwardToBurp(requestRecord);

        console.groupEnd();
    }

    // Hook XMLHttpRequest
    window.XMLHttpRequest = function() {
        const xhr = new originalXHR();
        const originalSend = xhr.send;
        const originalOpen = xhr.open;
        const originalSetRequestHeader = xhr.setRequestHeader;

        let requestData = {
            headers: {}
        };

        xhr.open = function(method, url, ...args) {
            requestData.method = method;
            requestData.url = url;
            return originalOpen.apply(this, arguments);
        };

        xhr.setRequestHeader = function(name, value) {
            requestData.headers[name] = value;
            return originalSetRequestHeader.apply(this, arguments);
        };

        xhr.send = function(data) {
            requestData.body = data;
            requestData.timestamp = Date.now();

            // 记录请求
            logRequest('XHR', requestData);

            return originalSend.apply(this, arguments);
        };

        return xhr;
    };

    // 保持XMLHttpRequest的原型链
    window.XMLHttpRequest.prototype = originalXHR.prototype;

    // Hook Fetch
    window.fetch = function(url, options = {}) {
        const requestData = {
            url: url,
            method: options.method || 'GET',
            headers: options.headers || {},
            body: options.body,
            timestamp: Date.now()
        };

        // 记录请求
        logRequest('FETCH', requestData);

        return originalFetch.apply(this, arguments);
    };

    // Hook常见的加密/编码函数来捕获原始数据
    const originalJSONStringify = JSON.stringify;
    const originalTextEncoderEncode = TextEncoder.prototype.encode;

    // Hook JSON.stringify
    JSON.stringify = function(...args) {
        const result = originalJSONStringify.apply(this, args);
        if (args[0] && typeof args[0] === 'object') {
            // 过滤掉一些不重要的对象
            const objStr = result;
            if (objStr && objStr.length > 10 && !objStr.includes('"__ob__"') && !objStr.includes('"$')) {
                const capturedData = {
                    type: 'JSON.stringify',
                    timestamp: new Date().toISOString(),
                    originalObject: args[0],
                    jsonString: result,
                    url: window.location.href
                };

                // 保存到全局数组
                window.capturedRequests.push(capturedData);

                console.group('🔧 JSON.stringify - 原始业务数据捕获');
                console.log('📋 原始对象:', args[0]);
                console.log('📝 JSON字符串:', result);
                console.log('⏰ 时间戳:', new Date().toLocaleString());
                console.log('💾 已保存到 window.capturedRequests[' + (window.capturedRequests.length - 1) + ']');
                console.groupEnd();
            }
        }
        return result;
    };

    // Hook TextEncoder.encode
    TextEncoder.prototype.encode = function(input) {
        const result = originalTextEncoderEncode.call(this, input);
        if (input && typeof input === 'string' && input.length > 10) {
            console.group('🔧 TextEncoder.encode called');
            console.log('Original string:', input);
            console.log('Encoded result:', result);
            console.groupEnd();
        }
        return result;
    };

    // Hook常见的加密库函数（如果存在）
    setTimeout(() => {
        // 检查是否有CryptoJS
        if (window['CryptoJS']) {
            console.log('🔐 CryptoJS detected, hooking encryption functions...');

            const CryptoJS = window['CryptoJS'];
            // Hook AES加密
            if (CryptoJS.AES && CryptoJS.AES.encrypt) {
                const originalAESEncrypt = CryptoJS.AES.encrypt;
                CryptoJS.AES.encrypt = function(message, key, cfg) {
                    console.group('🔐 CryptoJS AES.encrypt called');
                    console.log('Original message:', message);
                    console.log('Key:', key);
                    console.log('Config:', cfg);
                    const result = originalAESEncrypt.apply(this, arguments);
                    console.log('Encrypted result:', result.toString());
                    console.groupEnd();
                    return result;
                };
            }
        }

        // 检查是否有其他加密库
        if (window.crypto && window.crypto.subtle) {
            console.log('🔐 Web Crypto API detected');
        }
    }, 1000);

    console.log('[Hook Requests] Enhanced hooks installed - XHR, Fetch, and encryption functions');
})();