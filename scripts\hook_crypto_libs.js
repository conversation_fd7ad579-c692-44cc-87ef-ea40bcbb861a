// ==UserScript==
// @name         hook_crypto_libs
// @namespace    https://github.com/0xsdeo/Hook_JS
// @version      2024-12-20
// @description  Hook常见的加密库，捕获加密前的原始数据
// <AUTHOR>
// @match        http://*/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('[Crypto Hook] Encryption libraries interceptor loaded');
    
    // Hook CryptoJS
    function hookCryptoJS() {
        const checkCryptoJS = () => {
            if (window['CryptoJS']) {
                const CryptoJS = window['CryptoJS'];
                console.log('🔐 CryptoJS library detected');
                
                // Hook AES
                if (CryptoJS.AES) {
                    if (CryptoJS.AES.encrypt) {
                        const originalAESEncrypt = CryptoJS.AES.encrypt;
                        CryptoJS.AES.encrypt = function(message, key, cfg) {
                            console.group('🔐 CryptoJS AES.encrypt');
                            console.log('📝 Message:', message);
                            console.log('🔑 Key:', key);
                            console.log('⚙️ Config:', cfg);
                            const result = originalAESEncrypt.apply(this, arguments);
                            console.log('🔒 Encrypted:', result.toString());
                            console.groupEnd();
                            return result;
                        };
                    }
                    
                    if (CryptoJS.AES.decrypt) {
                        const originalAESDecrypt = CryptoJS.AES.decrypt;
                        CryptoJS.AES.decrypt = function(ciphertext, key, cfg) {
                            console.group('🔐 CryptoJS AES.decrypt');
                            console.log('🔒 Ciphertext:', ciphertext);
                            console.log('🔑 Key:', key);
                            console.log('⚙️ Config:', cfg);
                            const result = originalAESDecrypt.apply(this, arguments);
                            console.log('📝 Decrypted:', result.toString());
                            console.groupEnd();
                            return result;
                        };
                    }
                }
                
                // Hook DES
                if (CryptoJS.DES && CryptoJS.DES.encrypt) {
                    const originalDESEncrypt = CryptoJS.DES.encrypt;
                    CryptoJS.DES.encrypt = function(message, key, cfg) {
                        console.group('🔐 CryptoJS DES.encrypt');
                        console.log('📝 Message:', message);
                        console.log('🔑 Key:', key);
                        console.log('⚙️ Config:', cfg);
                        const result = originalDESEncrypt.apply(this, arguments);
                        console.log('🔒 Encrypted:', result.toString());
                        console.groupEnd();
                        return result;
                    };
                }
                
                // Hook MD5
                if (CryptoJS.MD5) {
                    const originalMD5 = CryptoJS.MD5;
                    CryptoJS.MD5 = function(message) {
                        console.group('🔐 CryptoJS MD5');
                        console.log('📝 Message:', message);
                        const result = originalMD5.apply(this, arguments);
                        console.log('🔒 Hash:', result.toString());
                        console.groupEnd();
                        return result;
                    };
                }
                
                // Hook SHA256
                if (CryptoJS.SHA256) {
                    const originalSHA256 = CryptoJS.SHA256;
                    CryptoJS.SHA256 = function(message) {
                        console.group('🔐 CryptoJS SHA256');
                        console.log('📝 Message:', message);
                        const result = originalSHA256.apply(this, arguments);
                        console.log('🔒 Hash:', result.toString());
                        console.groupEnd();
                        return result;
                    };
                }
                
                console.log('✅ CryptoJS hooks installed');
                return true;
            }
            return false;
        };
        
        // 立即检查
        if (!checkCryptoJS()) {
            // 延迟检查
            setTimeout(checkCryptoJS, 1000);
            setTimeout(checkCryptoJS, 3000);
            setTimeout(checkCryptoJS, 5000);
        }
    }
    
    // Hook JSEncrypt (RSA)
    function hookJSEncrypt() {
        const checkJSEncrypt = () => {
            if (window['JSEncrypt']) {
                const JSEncrypt = window['JSEncrypt'];
                console.log('🔐 JSEncrypt library detected');
                
                const originalEncrypt = JSEncrypt.prototype.encrypt;
                const originalDecrypt = JSEncrypt.prototype.decrypt;
                
                if (originalEncrypt) {
                    JSEncrypt.prototype.encrypt = function(text) {
                        console.group('🔐 JSEncrypt.encrypt');
                        console.log('📝 Text:', text);
                        const result = originalEncrypt.call(this, text);
                        console.log('🔒 Encrypted:', result);
                        console.groupEnd();
                        return result;
                    };
                }
                
                if (originalDecrypt) {
                    JSEncrypt.prototype.decrypt = function(text) {
                        console.group('🔐 JSEncrypt.decrypt');
                        console.log('🔒 Encrypted text:', text);
                        const result = originalDecrypt.call(this, text);
                        console.log('📝 Decrypted:', result);
                        console.groupEnd();
                        return result;
                    };
                }
                
                console.log('✅ JSEncrypt hooks installed');
                return true;
            }
            return false;
        };
        
        if (!checkJSEncrypt()) {
            setTimeout(checkJSEncrypt, 1000);
            setTimeout(checkJSEncrypt, 3000);
        }
    }
    
    // Hook Web Crypto API
    function hookWebCrypto() {
        if (window.crypto && window.crypto.subtle) {
            console.log('🔐 Web Crypto API detected');
            
            const originalEncrypt = window.crypto.subtle.encrypt;
            const originalDecrypt = window.crypto.subtle.decrypt;
            
            if (originalEncrypt) {
                window.crypto.subtle.encrypt = function(algorithm, key, data) {
                    console.group('🔐 Web Crypto encrypt');
                    console.log('🔧 Algorithm:', algorithm);
                    console.log('🔑 Key:', key);
                    console.log('📝 Data:', data);
                    console.groupEnd();
                    return originalEncrypt.apply(this, arguments);
                };
            }
            
            if (originalDecrypt) {
                window.crypto.subtle.decrypt = function(algorithm, key, data) {
                    console.group('🔐 Web Crypto decrypt');
                    console.log('🔧 Algorithm:', algorithm);
                    console.log('🔑 Key:', key);
                    console.log('🔒 Data:', data);
                    console.groupEnd();
                    return originalDecrypt.apply(this, arguments);
                };
            }
            
            console.log('✅ Web Crypto API hooks installed');
        }
    }
    
    // Hook 自定义加密函数（通过函数名检测）
    function hookCustomCrypto() {
        const cryptoFunctionNames = [
            'encrypt', 'decrypt', 'encode', 'decode', 'cipher', 'decipher',
            'hash', 'md5', 'sha1', 'sha256', 'aes', 'des', 'rsa'
        ];
        
        // 监听全局函数定义
        const originalDefineProperty = Object.defineProperty;
        Object.defineProperty = function(obj, prop, descriptor) {
            if (typeof prop === 'string' && cryptoFunctionNames.some(name => 
                prop.toLowerCase().includes(name))) {
                console.log(`🔍 Potential crypto function detected: ${prop}`);
            }
            return originalDefineProperty.apply(this, arguments);
        };
    }
    
    // 执行所有Hook
    hookCryptoJS();
    hookJSEncrypt();
    hookWebCrypto();
    hookCustomCrypto();
    
    console.log('[Crypto Hook] All encryption library hooks installed');
})();
