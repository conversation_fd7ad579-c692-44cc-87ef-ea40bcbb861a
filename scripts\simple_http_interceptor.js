// ==UserScript==
// @name         simple_http_interceptor
// @namespace    https://github.com/0xsdeo/Hook_JS
// @version      2024-12-20
// @description  简单直接的HTTP请求拦截器，确保捕获所有请求
// <AUTHOR>
// @match        http://*/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🚀 [Simple HTTP Interceptor] 加载完成');
    
    // 初始化全局变量
    window.allRequests = window.allRequests || [];
    window.requestCounter = window.requestCounter || 0;
    
    // Burp配置
    window.simpleBurpConfig = {
        enabled: false,
        host: '127.0.0.1',
        port: '8080'
    };
    
    // 启用Burp转发的简化函数
    window.enableSimpleBurp = function(host = '127.0.0.1', port = '8080') {
        window.simpleBurpConfig.enabled = true;
        window.simpleBurpConfig.host = host;
        window.simpleBurpConfig.port = port;
        console.log(`🔄 简单Burp转发已启用: ${host}:${port}`);
    };
    
    window.disableSimpleBurp = function() {
        window.simpleBurpConfig.enabled = false;
        console.log('❌ 简单Burp转发已禁用');
    };
    
    // 记录请求的函数
    function logAndForwardRequest(type, method, url, headers, body) {
        window.requestCounter++;
        
        const requestData = {
            id: window.requestCounter,
            type: type,
            method: method,
            url: url,
            headers: headers || {},
            body: body,
            timestamp: new Date().toISOString(),
            time: new Date().toLocaleString()
        };
        
        window.allRequests.push(requestData);
        
        console.group(`🌐 [${type}] HTTP请求 #${window.requestCounter}`);
        console.log('📍 URL:', url);
        console.log('📝 Method:', method);
        console.log('📋 Headers:', headers);
        console.log('📦 Body:', body);
        console.log('⏰ 时间:', requestData.time);
        console.groupEnd();
        
        // 转发到Burp
        if (window.simpleBurpConfig.enabled) {
            forwardToBurpSimple(requestData);
        }
        
        return requestData;
    }
    
    // 简化的Burp转发函数
    function forwardToBurpSimple(requestData) {
        try {
            const burpUrl = `http://${window.simpleBurpConfig.host}:${window.simpleBurpConfig.port}`;
            
            // 创建一个新的请求发送到Burp
            const burpRequest = new XMLHttpRequest();
            burpRequest.open('POST', burpUrl + '/intercepted-request', true);
            burpRequest.setRequestHeader('Content-Type', 'application/json');
            burpRequest.setRequestHeader('X-Intercepted-From', 'AntiDebug-Breaker');
            
            const payload = JSON.stringify(requestData);
            
            burpRequest.onload = function() {
                console.log('📤 请求已发送到Burp Suite');
            };
            
            burpRequest.onerror = function() {
                console.warn('⚠️ 发送到Burp失败，请检查Burp是否运行在', burpUrl);
            };
            
            burpRequest.send(payload);
            
        } catch (error) {
            console.warn('⚠️ Burp转发错误:', error.message);
        }
    }
    
    // Hook XMLHttpRequest
    const originalXHR = window.XMLHttpRequest;
    window.XMLHttpRequest = function() {
        const xhr = new originalXHR();
        const originalOpen = xhr.open;
        const originalSend = xhr.send;
        const originalSetRequestHeader = xhr.setRequestHeader;
        
        let requestInfo = {
            method: '',
            url: '',
            headers: {}
        };
        
        xhr.open = function(method, url, async, user, password) {
            requestInfo.method = method;
            requestInfo.url = url;
            return originalOpen.apply(this, arguments);
        };
        
        xhr.setRequestHeader = function(name, value) {
            requestInfo.headers[name] = value;
            return originalSetRequestHeader.apply(this, arguments);
        };
        
        xhr.send = function(data) {
            // 记录请求
            logAndForwardRequest('XHR', requestInfo.method, requestInfo.url, requestInfo.headers, data);
            return originalSend.apply(this, arguments);
        };
        
        return xhr;
    };
    window.XMLHttpRequest.prototype = originalXHR.prototype;
    
    // Hook Fetch
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        const method = options.method || 'GET';
        const headers = options.headers || {};
        const body = options.body;
        
        // 记录请求
        logAndForwardRequest('FETCH', method, url, headers, body);
        
        return originalFetch.apply(this, arguments);
    };
    
    // 导出函数
    window.exportAllRequests = function() {
        const data = JSON.stringify(window.allRequests, null, 2);
        const blob = new Blob([data], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `all_requests_${new Date().getTime()}.json`;
        a.click();
        URL.revokeObjectURL(url);
        console.log('📁 所有请求已导出');
    };
    
    window.clearAllRequests = function() {
        window.allRequests = [];
        window.requestCounter = 0;
        console.log('🗑️ 已清空所有请求记录');
    };
    
    window.showRequestStats = function() {
        console.group('📊 请求统计');
        console.log('总请求数:', window.allRequests.length);
        console.log('XHR请求:', window.allRequests.filter(r => r.type === 'XHR').length);
        console.log('Fetch请求:', window.allRequests.filter(r => r.type === 'FETCH').length);
        console.log('最新10个请求:', window.allRequests.slice(-10));
        console.groupEnd();
    };
    
    // 显示使用说明
    console.group('📖 简单HTTP拦截器使用说明');
    console.log('1. 启用Burp转发: enableSimpleBurp()');
    console.log('2. 禁用Burp转发: disableSimpleBurp()');
    console.log('3. 查看统计: showRequestStats()');
    console.log('4. 导出请求: exportAllRequests()');
    console.log('5. 清空记录: clearAllRequests()');
    console.log('6. 查看所有请求: console.log(window.allRequests)');
    console.groupEnd();
    
    console.log('✅ [Simple HTTP Interceptor] 准备就绪，开始拦截HTTP请求');
})();
