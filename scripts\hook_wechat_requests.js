// ==UserScript==
// @name         hook_wechat_requests
// @namespace    https://github.com/0xsdeo/Hook_JS
// @version      2024-12-20
// @description  专门针对微信小程序请求的拦截，捕获加密前的原始数据
// <AUTHOR>
// @match        http://*/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('[WeChat Hook] Specialized WeChat Mini Program request interceptor loaded');
    
    // 微信小程序相关的Hook
    function hookWeChatAPIs() {
        // Hook wx.request (如果存在)
        if (window.wx && window.wx.request) {
            const originalWxRequest = window.wx.request;
            window.wx.request = function(options) {
                console.group('📱 wx.request called');
                console.log('Options:', options);
                console.log('URL:', options.url);
                console.log('Data:', options.data);
                console.log('Method:', options.method);
                console.log('Header:', options.header);
                console.groupEnd();
                
                return originalWxRequest.call(this, options);
            };
            console.log('✅ wx.request hooked');
        }
        
        // Hook 微信云开发相关API
        if (window.wx && window.wx.cloud) {
            console.log('☁️ WeChat Cloud APIs detected');
            
            // Hook callFunction
            if (window.wx.cloud.callFunction) {
                const originalCallFunction = window.wx.cloud.callFunction;
                window.wx.cloud.callFunction = function(options) {
                    console.group('☁️ wx.cloud.callFunction called');
                    console.log('Function name:', options.name);
                    console.log('Data:', options.data);
                    console.log('Options:', options);
                    console.groupEnd();
                    
                    return originalCallFunction.call(this, options);
                };
                console.log('✅ wx.cloud.callFunction hooked');
            }
            
            // Hook database operations
            if (window.wx.cloud.database) {
                console.log('🗄️ WeChat Cloud Database detected');
            }
        }
    }
    
    // Hook 可能的加密函数
    function hookEncryptionFunctions() {
        // Hook 常见的编码函数
        const originalBtoa = window.btoa;
        const originalAtob = window.atob;
        
        if (originalBtoa) {
            window.btoa = function(str) {
                console.group('🔧 btoa (base64 encode) called');
                console.log('Original string:', str);
                const result = originalBtoa.call(this, str);
                console.log('Encoded result:', result);
                console.groupEnd();
                return result;
            };
        }
        
        if (originalAtob) {
            window.atob = function(str) {
                console.group('🔧 atob (base64 decode) called');
                console.log('Encoded string:', str);
                const result = originalAtob.call(this, str);
                console.log('Decoded result:', result);
                console.groupEnd();
                return result;
            };
        }
        
        // Hook encodeURIComponent
        const originalEncodeURIComponent = window.encodeURIComponent;
        window.encodeURIComponent = function(str) {
            if (str && str.length > 20) {
                console.group('🔧 encodeURIComponent called');
                console.log('Original:', str);
                const result = originalEncodeURIComponent.call(this, str);
                console.log('Encoded:', result);
                console.groupEnd();
                return result;
            }
            return originalEncodeURIComponent.call(this, str);
        };
    }
    
    // Hook WebSocket (微信小程序可能使用)
    function hookWebSocket() {
        const originalWebSocket = window.WebSocket;
        if (originalWebSocket) {
            window.WebSocket = function(url, protocols) {
                console.group('🔌 WebSocket connection');
                console.log('URL:', url);
                console.log('Protocols:', protocols);
                console.groupEnd();
                
                const ws = new originalWebSocket(url, protocols);
                
                // Hook send method
                const originalSend = ws.send;
                ws.send = function(data) {
                    console.group('🔌 WebSocket send');
                    console.log('Data:', data);
                    console.groupEnd();
                    return originalSend.call(this, data);
                };
                
                return ws;
            };
            window.WebSocket.prototype = originalWebSocket.prototype;
            console.log('✅ WebSocket hooked');
        }
    }
    
    // 立即执行Hook
    hookWeChatAPIs();
    hookEncryptionFunctions();
    hookWebSocket();
    
    // 延迟执行，等待页面加载完成后再次检查
    setTimeout(() => {
        console.log('[WeChat Hook] Re-checking for WeChat APIs...');
        hookWeChatAPIs();
    }, 2000);
    
    // 监听页面中可能动态加载的脚本
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.tagName === 'SCRIPT') {
                    setTimeout(() => {
                        hookWeChatAPIs();
                    }, 100);
                }
            });
        });
    });
    
    observer.observe(document, {
        childList: true,
        subtree: true
    });
    
    console.log('[WeChat Hook] All hooks installed and monitoring enabled');
})();
