// ==UserScript==
// @name         burp_forwarder
// @namespace    https://github.com/0xsdeo/Hook_JS
// @version      2024-12-20
// @description  将拦截的请求转发到Burp Suite进行分析
// <AUTHOR>
// @match        http://*/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('[Burp Forwarder] Request forwarding to Burp Suite loaded');
    
    // Burp Suite配置
    window.burpForwarder = {
        enabled: false,
        proxyHost: '127.0.0.1',
        proxyPort: '8080',
        interceptAll: true,
        logRequests: true
    };
    
    // 启用Burp转发
    window.enableBurpForward = function(host = '127.0.0.1', port = '8080') {
        window.burpForwarder.enabled = true;
        window.burpForwarder.proxyHost = host;
        window.burpForwarder.proxyPort = port;
        console.log(`🔄 Burp转发已启用: ${host}:${port}`);
        console.log('💡 提示: 确保Burp Suite代理监听在该地址上');
    };
    
    // 禁用Burp转发
    window.disableBurpForward = function() {
        window.burpForwarder.enabled = false;
        console.log('❌ Burp转发已禁用');
    };
    
    // 创建代理请求
    function createProxyRequest(originalUrl, method, headers, body) {
        const url = new URL(originalUrl);
        const proxyUrl = `http://${window.burpForwarder.proxyHost}:${window.burpForwarder.proxyPort}${url.pathname}${url.search}`;
        
        // 添加原始主机头
        const proxyHeaders = {
            ...headers,
            'Host': url.host,
            'X-Forwarded-For': '127.0.0.1',
            'X-Forwarded-Proto': url.protocol.slice(0, -1),
            'X-Original-URL': originalUrl
        };
        
        return {
            url: proxyUrl,
            method: method,
            headers: proxyHeaders,
            body: body
        };
    }
    
    // Hook XMLHttpRequest
    const originalXHR = window.XMLHttpRequest;
    window.XMLHttpRequest = function() {
        const xhr = new originalXHR();
        const originalOpen = xhr.open;
        const originalSend = xhr.send;
        const originalSetRequestHeader = xhr.setRequestHeader;
        
        let requestData = {
            method: 'GET',
            url: '',
            headers: {},
            body: null
        };
        
        xhr.open = function(method, url, async, user, password) {
            requestData.method = method;
            requestData.url = url;
            
            // 如果启用了Burp转发，修改URL
            if (window.burpForwarder.enabled && window.burpForwarder.interceptAll) {
                const proxyRequest = createProxyRequest(url, method, requestData.headers, requestData.body);
                return originalOpen.call(this, method, proxyRequest.url, async, user, password);
            }
            
            return originalOpen.call(this, method, url, async, user, password);
        };
        
        xhr.setRequestHeader = function(name, value) {
            requestData.headers[name] = value;
            return originalSetRequestHeader.call(this, name, value);
        };
        
        xhr.send = function(data) {
            requestData.body = data;
            
            if (window.burpForwarder.enabled) {
                if (window.burpForwarder.logRequests) {
                    console.group('📤 XHR Request → Burp Suite');
                    console.log('🌐 Original URL:', requestData.url);
                    console.log('📝 Method:', requestData.method);
                    console.log('📋 Headers:', requestData.headers);
                    console.log('📦 Body:', data);
                    console.groupEnd();
                }
                
                // 如果启用了拦截，设置代理头
                if (window.burpForwarder.interceptAll) {
                    const proxyRequest = createProxyRequest(requestData.url, requestData.method, requestData.headers, data);
                    // 设置代理头
                    Object.entries(proxyRequest.headers).forEach(([key, value]) => {
                        if (key !== 'Host') { // Host头由浏览器自动设置
                            xhr.setRequestHeader(key, value);
                        }
                    });
                }
            }
            
            return originalSend.call(this, data);
        };
        
        return xhr;
    };
    window.XMLHttpRequest.prototype = originalXHR.prototype;
    
    // Hook Fetch
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
        if (window.burpForwarder.enabled) {
            const method = options.method || 'GET';
            const headers = options.headers || {};
            const body = options.body;
            
            if (window.burpForwarder.logRequests) {
                console.group('📤 Fetch Request → Burp Suite');
                console.log('🌐 Original URL:', url);
                console.log('📝 Method:', method);
                console.log('📋 Headers:', headers);
                console.log('📦 Body:', body);
                console.groupEnd();
            }
            
            if (window.burpForwarder.interceptAll) {
                const proxyRequest = createProxyRequest(url, method, headers, body);
                return originalFetch.call(this, proxyRequest.url, {
                    ...options,
                    headers: proxyRequest.headers
                });
            }
        }
        
        return originalFetch.call(this, url, options);
    };
    
    // 添加手动发送请求到Burp的功能
    window.sendToBurp = function(url, method = 'GET', headers = {}, body = null) {
        if (!window.burpForwarder.enabled) {
            console.warn('⚠️ Burp转发未启用，请先调用 enableBurpForward()');
            return;
        }
        
        const proxyRequest = createProxyRequest(url, method, headers, body);
        
        console.group('📤 手动发送到Burp Suite');
        console.log('🌐 Original URL:', url);
        console.log('🔄 Proxy URL:', proxyRequest.url);
        console.log('📝 Method:', method);
        console.log('📋 Headers:', proxyRequest.headers);
        console.log('📦 Body:', body);
        console.groupEnd();
        
        return fetch(proxyRequest.url, {
            method: method,
            headers: proxyRequest.headers,
            body: body
        });
    };
    
    // 显示使用说明
    console.group('📖 Burp Forwarder 使用说明');
    console.log('1. 启用转发: enableBurpForward("127.0.0.1", "8080")');
    console.log('2. 禁用转发: disableBurpForward()');
    console.log('3. 手动发送: sendToBurp(url, method, headers, body)');
    console.log('4. 确保Burp Suite代理监听在指定地址');
    console.groupEnd();
    
    console.log('[Burp Forwarder] Ready to forward requests to Burp Suite');
})();
