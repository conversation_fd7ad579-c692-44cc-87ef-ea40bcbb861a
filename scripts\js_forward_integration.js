// ==UserScript==
// @name         JS-Forward Integration
// @namespace    https://github.com/0xsdeo/Hook_JS
// @version      2024-12-20
// @description  基于JS-Forward思路的参数转发器，支持在加密前拦截和修改参数
// <AUTHOR>
// @match        http://*/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    console.log('🚀 [JS-Forward Integration] 参数转发器已加载');

    // 配置
    const JS_FORWARD_CONFIG = {
        forwardPort: 28080,
        echoPort: 38080,
        burpPort: 8080,
        enabled: false,
        autoInject: true
    };

    // 存储
    window.jsForwardData = {
        interceptedParams: [],
        injectedFunctions: [],
        config: JS_FORWARD_CONFIG
    };

    // 1. 启用JS-Forward模式
    window.enableJSForward = function() {
        JS_FORWARD_CONFIG.enabled = true;
        console.log('🔄 [JS-Forward] 已启用，转发端口:', JS_FORWARD_CONFIG.forwardPort);
        
        // 自动注入常见的参数拦截
        if (JS_FORWARD_CONFIG.autoInject) {
            autoInjectCommonParams();
        }
        
        return true;
    };

    // 2. 生成JS-Forward Payload
    window.generateJSForwardPayload = function(paramName, dataType = 'json', requestType = 'REQUEST') {
        let payload;
        
        if (dataType === 'json') {
            payload = `var xhr = new XMLHttpRequest();xhr.open("post", "http://127.0.0.1:${JS_FORWARD_CONFIG.forwardPort}/${requestType}", false);xhr.send(JSON.stringify(${paramName}));${paramName}=JSON.parse(xhr.responseText);`;
        } else if (dataType === 'string') {
            payload = `var xhr = new XMLHttpRequest();xhr.open("post", "http://127.0.0.1:${JS_FORWARD_CONFIG.forwardPort}/${requestType}", false);xhr.send(${paramName});${paramName}=xhr.responseText;`;
        } else {
            console.error('❌ 不支持的数据类型:', dataType);
            return null;
        }
        
        console.group('📋 [JS-Forward] Payload生成完毕');
        console.log('参数名:', paramName);
        console.log('数据类型:', dataType);
        console.log('请求类型:', requestType);
        console.log('Payload:', payload);
        console.groupEnd();
        
        return payload;
    };

    // 3. 动态注入参数拦截
    window.injectParamInterceptor = function(paramName, dataType = 'json', requestType = 'REQUEST') {
        if (!JS_FORWARD_CONFIG.enabled) {
            console.warn('⚠️ 请先启用JS-Forward模式');
            return false;
        }
        
        const payload = generateJSForwardPayload(paramName, dataType, requestType);
        if (!payload) return false;
        
        try {
            // 创建一个函数来包装原始逻辑
            const wrapperFunction = new Function('originalParam', `
                console.log('🔍 [JS-Forward] 拦截参数:', '${paramName}', originalParam);
                
                // 保存原始参数
                window.jsForwardData.interceptedParams.push({
                    name: '${paramName}',
                    original: originalParam,
                    timestamp: new Date().toISOString()
                });
                
                // 执行转发逻辑
                let ${paramName} = originalParam;
                ${payload}
                
                console.log('📤 [JS-Forward] 参数已转发并可能被修改:', ${paramName});
                return ${paramName};
            `);
            
            // 保存注入信息
            window.jsForwardData.injectedFunctions.push({
                paramName: paramName,
                dataType: dataType,
                requestType: requestType,
                payload: payload,
                wrapper: wrapperFunction,
                timestamp: new Date().toISOString()
            });
            
            console.log(`✅ [JS-Forward] 参数拦截器已注入: ${paramName}`);
            return wrapperFunction;
            
        } catch (error) {
            console.error('❌ [JS-Forward] 注入失败:', error);
            return false;
        }
    };

    // 4. 自动注入常见参数
    function autoInjectCommonParams() {
        console.log('🔄 [JS-Forward] 开始自动注入常见参数拦截器...');
        
        // 常见的参数名列表
        const commonParams = [
            { name: 'data', type: 'json' },
            { name: 'params', type: 'json' },
            { name: 'body', type: 'string' },
            { name: 'payload', type: 'json' },
            { name: 'requestData', type: 'json' },
            { name: 'postData', type: 'json' }
        ];
        
        commonParams.forEach(param => {
            injectParamInterceptor(param.name, param.type, 'REQUEST');
        });
        
        console.log(`✅ [JS-Forward] 已注入 ${commonParams.length} 个常见参数拦截器`);
    }

    // 5. Hook JSON.stringify 进行自动转发
    const originalJSONStringify = JSON.stringify;
    JSON.stringify = function(...args) {
        const result = originalJSONStringify.apply(this, args);
        
        if (JS_FORWARD_CONFIG.enabled && args[0] && typeof args[0] === 'object') {
            // 过滤有效的业务数据
            if (result && result.length > 20 && !result.includes('"__ob__"')) {
                console.log('🔍 [JS-Forward] 检测到JSON.stringify调用');
                
                // 自动发送到转发服务器
                sendToForwardServer(result, 'JSON_STRINGIFY');
                
                // 保存拦截数据
                window.jsForwardData.interceptedParams.push({
                    name: 'JSON.stringify',
                    original: args[0],
                    jsonString: result,
                    timestamp: new Date().toISOString(),
                    type: 'auto-intercepted'
                });
            }
        }
        
        return result;
    };

    // 6. 发送数据到转发服务器
    function sendToForwardServer(data, requestType = 'REQUEST') {
        if (!JS_FORWARD_CONFIG.enabled) return;
        
        try {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', `http://127.0.0.1:${JS_FORWARD_CONFIG.forwardPort}/${requestType}`, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('X-JS-Forward', 'true');
            xhr.setRequestHeader('X-Source', 'AntiDebug-Breaker');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        console.log(`📤 [JS-Forward] 数据已发送到转发服务器: ${requestType}`);
                    } else {
                        console.log(`📤 [JS-Forward] 转发服务器响应: ${xhr.status} (这是正常的，数据已通过Burp)`);
                    }
                }
            };
            
            xhr.send(data);
            
        } catch (error) {
            console.log('📤 [JS-Forward] 数据已发送 (连接错误是正常的):', error.message);
        }
    }

    // 7. 批量转发已捕获的数据
    window.forwardCapturedDataToJS = function() {
        if (!JS_FORWARD_CONFIG.enabled) {
            console.warn('⚠️ 请先启用JS-Forward模式');
            return;
        }
        
        console.log('🔄 [JS-Forward] 开始批量转发已捕获的数据...');
        
        // 转发JSON.stringify捕获的数据
        if (window.capturedRequests) {
            const jsonData = window.capturedRequests.filter(req => req.type === 'JSON.stringify');
            console.log(`📋 找到 ${jsonData.length} 条JSON数据`);
            
            jsonData.slice(-5).forEach((item, index) => {
                setTimeout(() => {
                    sendToForwardServer(item.jsonString, 'CAPTURED_JSON');
                }, index * 500);
            });
        }
        
        // 转发拦截的参数
        const interceptedData = window.jsForwardData.interceptedParams;
        console.log(`📊 找到 ${interceptedData.length} 条拦截参数`);
        
        interceptedData.slice(-5).forEach((item, index) => {
            setTimeout(() => {
                const data = item.jsonString || JSON.stringify(item.original);
                sendToForwardServer(data, 'INTERCEPTED_PARAM');
            }, (5 + index) * 500);
        });
        
        console.log('✅ [JS-Forward] 批量转发完成');
    };

    // 8. 状态查看
    window.jsForwardStatus = function() {
        console.group('📊 [JS-Forward] 状态报告');
        console.log('🔧 转发状态:', JS_FORWARD_CONFIG.enabled ? '✅ 已启用' : '❌ 未启用');
        console.log('🎯 转发端口:', JS_FORWARD_CONFIG.forwardPort);
        console.log('📋 拦截参数数量:', window.jsForwardData.interceptedParams.length);
        console.log('🔧 注入函数数量:', window.jsForwardData.injectedFunctions.length);
        console.log('📊 配置信息:', JS_FORWARD_CONFIG);
        console.groupEnd();
        
        return window.jsForwardData;
    };

    // 9. 使用说明
    console.group('📖 [JS-Forward Integration] 使用说明');
    console.log('1. 启用转发: enableJSForward()');
    console.log('2. 生成Payload: generateJSForwardPayload("paramName", "json", "REQUEST")');
    console.log('3. 注入拦截器: injectParamInterceptor("data", "json", "REQUEST")');
    console.log('4. 转发已捕获数据: forwardCapturedDataToJS()');
    console.log('5. 查看状态: jsForwardStatus()');
    console.log('6. 注意: 需要先启动JS-Forward Python服务器');
    console.groupEnd();

    console.log('✅ [JS-Forward Integration] 准备就绪');
    console.log('💡 提示: 请先运行 python3 Js-forward.py 启动转发服务器');

})();
