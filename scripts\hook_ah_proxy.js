// ==UserScript==
// @name         hook_ah_proxy
// @namespace    https://github.com/0xsdeo/Hook_JS
// @version      2024-12-20
// @description  Hook ah.proxy代理拦截器，捕获请求配置和处理过程
// <AUTHOR>
// @match        http://*/*
// @icon         data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🔧 [ah.proxy Hook] 代理拦截器Hook已加载');
    
    // 初始化存储
    window.ahProxyRequests = window.ahProxyRequests || [];
    let requestId = 0;
    
    // 等待ah对象加载
    function waitForAhProxy() {
        if (window.ah && window.ah.proxy) {
            hookAhProxy();
        } else {
            setTimeout(waitForAhProxy, 100);
        }
    }
    
    function hookAhProxy() {
        console.log('✅ 找到ah.proxy，开始Hook');
        
        const originalProxy = window.ah.proxy;
        
        window.ah.proxy = function(config) {
            requestId++;
            
            console.group(`🔧 [ah.proxy] 请求拦截 #${requestId}`);
            console.log('📋 原始配置:', config);
            
            // 保存原始配置
            const originalConfig = JSON.parse(JSON.stringify(config));
            
            // 调用原始proxy函数
            const result = originalProxy.call(this, {
                ...config,
                onRequest: async (requestConfig, handler) => {
                    console.group(`📤 [ah.proxy] onRequest处理 #${requestId}`);
                    console.log('🌐 请求URL:', requestConfig.url);
                    console.log('📝 请求方法:', requestConfig.method);
                    console.log('📋 请求头:', requestConfig.headers);
                    console.log('📦 请求体类型:', typeof requestConfig.body);
                    console.log('📦 请求体:', requestConfig.body);
                    
                    // 特殊处理FormData
                    if (requestConfig.body instanceof FormData) {
                        console.log('📋 FormData内容:');
                        for (const [key, value] of requestConfig.body.entries()) {
                            console.log(`  ${key}:`, value);
                        }
                    }
                    
                    // 保存请求信息
                    const requestInfo = {
                        id: requestId,
                        timestamp: Date.now(),
                        time: new Date().toLocaleString(),
                        originalConfig: originalConfig,
                        processedConfig: {
                            url: requestConfig.url,
                            method: requestConfig.method,
                            headers: requestConfig.headers,
                            body: requestConfig.body,
                            bodyType: typeof requestConfig.body
                        },
                        type: 'ah.proxy'
                    };
                    
                    window.ahProxyRequests.push(requestInfo);
                    console.log('💾 已保存到 window.ahProxyRequests[' + (window.ahProxyRequests.length - 1) + ']');
                    console.groupEnd();
                    
                    // 转发到Burp（如果启用）
                    if (window.burpForAhProxy?.enabled) {
                        forwardAhProxyToBurp(requestInfo);
                    }
                    
                    // 调用原始的onRequest处理器
                    if (config.onRequest) {
                        return await config.onRequest(requestConfig, handler);
                    }
                    
                    return handler(requestConfig);
                }
            });
            
            console.groupEnd();
            return result;
        };
        
        console.log('✅ ah.proxy Hook完成');
    }
    
    // Burp转发配置
    window.burpForAhProxy = {
        enabled: false,
        host: '127.0.0.1',
        port: '8080'
    };
    
    // 启用ah.proxy的Burp转发
    window.enableAhProxyBurp = function(host = '127.0.0.1', port = '8080') {
        window.burpForAhProxy.enabled = true;
        window.burpForAhProxy.host = host;
        window.burpForAhProxy.port = port;
        console.log(`🔄 ah.proxy Burp转发已启用: ${host}:${port}`);
    };
    
    window.disableAhProxyBurp = function() {
        window.burpForAhProxy.enabled = false;
        console.log('❌ ah.proxy Burp转发已禁用');
    };
    
    // 转发ah.proxy请求到Burp
    function forwardAhProxyToBurp(requestInfo) {
        try {
            const burpUrl = `http://${window.burpForAhProxy.host}:${window.burpForAhProxy.port}`;
            
            // 构造转发请求
            const forwardRequest = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Forwarded-From': 'ah.proxy-hook',
                    'X-Original-URL': requestInfo.processedConfig.url,
                    'X-Original-Method': requestInfo.processedConfig.method || 'GET',
                    'X-Request-ID': requestInfo.id.toString(),
                    'X-Timestamp': requestInfo.timestamp.toString()
                },
                body: JSON.stringify({
                    source: 'ah.proxy',
                    requestInfo: requestInfo,
                    reconstructedRequest: {
                        url: requestInfo.processedConfig.url,
                        method: requestInfo.processedConfig.method || 'GET',
                        headers: requestInfo.processedConfig.headers,
                        body: requestInfo.processedConfig.bodyType === 'object' ? 
                              JSON.stringify(requestInfo.processedConfig.body) : 
                              requestInfo.processedConfig.body
                    }
                })
            };
            
            fetch(burpUrl, forwardRequest)
                .then(() => {
                    console.log(`📤 ah.proxy请求 #${requestInfo.id} 已转发到Burp`);
                })
                .catch(err => {
                    console.log(`📤 ah.proxy请求 #${requestInfo.id} 转发完成 (${err.message})`);
                });
                
        } catch (error) {
            console.warn('⚠️ ah.proxy Burp转发错误:', error.message);
        }
    }
    
    // 查看ah.proxy请求的函数
    window.showAhProxyRequests = function() {
        console.group('📊 ah.proxy 请求统计');
        console.log('总请求数:', window.ahProxyRequests.length);
        console.log('最近5个请求:', window.ahProxyRequests.slice(-5));
        
        // 按URL分组
        const urlGroups = {};
        window.ahProxyRequests.forEach(req => {
            const url = req.processedConfig.url;
            if (!urlGroups[url]) {
                urlGroups[url] = [];
            }
            urlGroups[url].push(req);
        });
        
        console.log('按URL分组:', urlGroups);
        console.groupEnd();
        
        return window.ahProxyRequests;
    };
    
    // 导出ah.proxy请求数据
    window.exportAhProxyData = function() {
        const exportData = {
            summary: {
                totalRequests: window.ahProxyRequests.length,
                exportTime: new Date().toISOString(),
                description: 'ah.proxy代理拦截的请求数据'
            },
            requests: window.ahProxyRequests
        };
        
        const dataStr = JSON.stringify(exportData, null, 2);
        const blob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ah_proxy_requests_${new Date().getTime()}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        console.log('📁 ah.proxy请求数据已导出');
        return exportData;
    };
    
    // 清空ah.proxy请求数据
    window.clearAhProxyData = function() {
        window.ahProxyRequests = [];
        requestId = 0;
        console.log('🗑️ 已清空ah.proxy请求数据');
    };
    
    // 开始等待并Hook
    waitForAhProxy();
    
    // 显示使用说明
    setTimeout(() => {
        console.group('📖 ah.proxy Hook 使用说明');
        console.log('1. 查看请求: showAhProxyRequests()');
        console.log('2. 启用Burp转发: enableAhProxyBurp()');
        console.log('3. 禁用Burp转发: disableAhProxyBurp()');
        console.log('4. 导出数据: exportAhProxyData()');
        console.log('5. 清空数据: clearAhProxyData()');
        console.log('6. 查看实时数据: window.ahProxyRequests');
        console.groupEnd();
    }, 1000);
    
    console.log('✅ [ah.proxy Hook] 准备就绪，等待ah对象加载...');
})();
