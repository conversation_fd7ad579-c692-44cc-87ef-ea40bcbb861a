/* 简约蓝色主题 */
:root {
    --primary: #1a73e8;
    --primary-light: #4285f4;
    --primary-dark: #0d47a1;
    --background: #0a192f;
    --surface: #112240;
    --surface-light: #1e3a5f;
    --on-surface: #e6f1ff;
    --on-surface-secondary: #a8b2d1;
    --border: #233554;
    --success: #00c853;
    --error: #ff5252;
    --card-height: 120px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
}

body {
    background: linear-gradient(135deg, var(--background) 0%, #07162b 100%);
    color: var(--on-surface);
    min-width: 500px;
    max-height: 600px;
    padding: 20px;
    overflow: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 0 5px;
    position: relative;
}

header {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border);
    position: relative;
    z-index: 10;
    background: transparent;
}

.title-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    margin-top: 5px;
    position: relative; /* 为GitHub图标定位提供参考 */
}

h1 {
    font-size: 28px;
    font-weight: 700;
    text-align: center;
    color: var(--on-surface);
    letter-spacing: 0.5px;
    position: relative;
}

.breaker {
    font-weight: 800;
    font-size: 32px;
    position: relative;
    color: var(--primary-light);
    text-shadow: 0 0 10px rgba(66, 133, 244, 0.5);
    letter-spacing: 1px;
}

.search-container {
    display: flex;
    background: var(--surface);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--border);
    transition: all 0.3s ease;
    margin: 0 5px;
}

.search-container:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.3);
}

#search-input {
    flex: 1;
    padding: 10px 15px;
    border: none;
    background: transparent;
    color: var(--on-surface);
    font-size: 14px;
    outline: none;
}

#search-input::placeholder {
    color: var(--on-surface-secondary);
}

#search-btn {
    padding: 0 15px;
    background: transparent;
    border: none;
    cursor: pointer;
    color: var(--on-surface-secondary);
    transition: color 0.2s;
}

#search-btn:hover {
    color: var(--primary);
}

.scripts-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    max-height: 420px;
    overflow-y: auto;
    padding: 8px;
    margin-top: 5px;
}

.script-item {
    background: var(--surface);
    border-radius: 8px;
    padding: 12px 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: var(--card-height);
    border: 1px solid var(--border);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.script-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3), 0 0 0 1px var(--primary);
    z-index: 5;
}

.script-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.script-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.script-name {
    font-weight: 600;
    font-size: 13px;
    color: var(--on-surface);
    line-height: 1.3;
    flex: 1;
}

.script-description {
    color: var(--on-surface-secondary);
    font-size: 11px;
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.switch {
    position: relative;
    display: inline-block;
    width: 36px;
    height: 20px;
    flex-shrink: 0;
    margin-left: 6px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #555;
    transition: .3s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 14px;
    width: 14px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .3s;
    border-radius: 50%;
}

input:checked + .slider {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

input:checked + .slider:before {
    transform: translateX(16px);
}

.no-results {
    display: none;
    text-align: center;
    padding: 20px 0;
    color: var(--on-surface-secondary);
    flex-direction: column;
    align-items: center;
    gap: 15px;
    grid-column: 1 / -1;
}

.no-results svg {
    width: 48px;
    height: 48px;
    color: var(--primary);
}

/* 底部区域优化 */
footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 5px;
    border-top: 1px solid var(--border);
    font-size: 12px;
    margin: 10px 5px 0;
    position: relative;
    z-index: 2;
    background: transparent;
}

/* 提示信息 - 移到左侧 */
.hint {
    font-style: italic;
    background: transparent;
    text-align: left; /* 左对齐 */
    padding: 5px 10px;
}

/* 署名信息 - 使用原计数样式 */
.signature {
    color: var(--primary-light);
    background: transparent;
    text-align: right;
    padding: 5px 10px;
    font-weight: bold;
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-light);
}

/* 活动脚本指示器 */
.script-item.active {
    position: relative;
}

.script-item.active::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    border-radius: 0 0 8px 8px;
}

.github-link {
    position: absolute;
    top: 13px;
    right: 15px;
    color: var(--on-surface);
    transition: color 0.3s;
    width: 24px;
    height: 24px;
    z-index: 20;
}

.github-link:hover {
    color: var(--primary-light);
    transform: scale(1.1);
}