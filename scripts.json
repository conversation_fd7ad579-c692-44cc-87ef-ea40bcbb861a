[{"id": "Bypass_Debugger", "name": "Bypass Debugger", "description": "绕过无限Debugger， 如绕不过去或报错请使用火狐忽略断点。"}, {"id": "hook_log v0.1", "name": "hook_log v0.1", "description": "防止js重写log方法，该版本不生效可以尝试使用v0.2版本。"}, {"id": "hook_log v0.2", "name": "hook_log v0.2", "description": "防止js重写log方法 注：v0.1和v0.2脚本不能混用"}, {"id": "hook_table", "name": "Hook table", "description": "绕过js检测运行时间差来实现反调试"}, {"id": "hook_clear", "name": "hook clear", "description": "禁止js清除控制台数据"}, {"id": "hook_close", "name": "hook close", "description": "重写close方法，以此来避免网站反调试关闭当前页面"}, {"id": "hook_history", "name": "hook history", "description": "避免网站反调试返回上一页或某个特定历史页面"}, {"id": "Fixed_window_size", "name": "<div style=\"font-size: 0.9em;\">Fixed window size</div>", "description": "固定浏览器高度宽度值以绕过前端检测用户是否打开控制台"}, {"id": "location_href", "name": "页面跳转JS代码定位通杀方案", "description": "阻断页面跳转，留在当前页面分析"}, {"id": "hook_requests", "name": "Hook Requests", "description": "拦截业务层请求，在加密前捕获原始数据（XHR和Fetch）"}, {"id": "hook_wechat_requests", "name": "Hook WeChat Requests", "description": "专门针对微信小程序的请求拦截，包括wx.request、云函数等"}, {"id": "hook_crypto_libs", "name": "Hook Crypto Libraries", "description": "拦截常见加密库（CryptoJS、JSEncrypt、Web Crypto API）的加密操作"}, {"id": "burp_forwarder", "name": "Burp Suite Forwarder", "description": "将所有HTTP请求转发到Burp Suite代理进行分析和修改"}, {"id": "simple_http_interceptor", "name": "Simple HTTP Interceptor", "description": "简单直接的HTTP请求拦截器，确保捕获所有XHR和Fetch请求"}, {"id": "hook_ah_proxy", "name": "Hook ah.proxy", "description": "拦截ah.proxy代理函数，捕获请求配置和处理过程，包括FormData处理"}, {"id": "js_forward_integration", "name": "JS-Forward Integration", "description": "基于JS-Forward思路的参数转发器，支持在加密前拦截和修改参数，需配合Python服务器使用"}]